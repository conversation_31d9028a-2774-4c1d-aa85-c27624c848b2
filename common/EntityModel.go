package common

type EntityModel int

const (
	WindowsPatch EntityModel = iota + 1
	ThirdPartyPackage
	PatchLanguage
	PatchCategory
	PatchProduct
	UbuntuPatch
	LinuxPackages
	UbuntuNoticeData
	UbuntuReleasePackage
	CentosPatch
	CentosNoticeData
	CentosReleasePackage
	MacOsPatch
	UpdateMsrcSyncHistory
	MsrcVulnerability
	MsrcSupercedences
	CabSyncHistory
)

func (oa EntityModel) String() string {
	switch oa {
	case WindowsPatch:
		return "windows_patches"
	case ThirdPartyPackage:
		return "third_party_packages"
	case PatchLanguage:
		return "languages"
	case PatchCategory:
		return "patch_categories"
	case PatchProduct:
		return "patch_products"
	case UbuntuPatch:
		return "ubuntu_patches"
	case LinuxPackages:
		return "linux_packages"
	case UbuntuNoticeData:
		return "ubuntu_notice_data"
	case UbuntuReleasePackage:
		return "ubuntu_release_packages"
	case CentosPatch:
		return "centos_patches"
	case CentosNoticeData:
		return "centos_notice_data"
	case CentosReleasePackage:
		return "centos_release_packages"
	case MacOsPatch:
		return "mac_os_patches"
	case UpdateMsrcSyncHistory:
		return "update_msrc_sync_histories"
	case MsrcVulnerability:
		return "msrc_vulnerabilities"
	case MsrcSupercedences:
		return "msrc_supercedences"
	case CabSyncHistory:
		return "cab_sync_histories"
	default:
		return ""
	}
}
