package db

import (
	"fmt"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"golang.org/x/net/context"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/linux/centos"
	"patch-central-repo/model/linux/ubuntu"
	"patch-central-repo/model/macos"
	"patch-central-repo/model/thirdparty"
	"patch-central-repo/model/windows"
	"strconv"
	"time"
)

var Connection *bun.DB

type dbLogger struct{}

func (q *dbLogger) BeforeQuery(ctx context.Context, _ *bun.QueryEvent) context.Context {
	return ctx
}

func (q *dbLogger) AfterQuery(_ context.Context, event *bun.QueryEvent) {
	if event.Err == nil {
		logger.DBQueryLogger.Info("query: ", event.Query)
	}
}

func Connect() (*bun.DB, error) {
	dbHost := common.GetEnv("DB_HOST", "localhost")
	dbPort := common.GetEnv("DB_PORT", strconv.Itoa(5432))
	dbUser := common.GetEnv("DB_USER", "postgres")
	dbPass := common.GetEnv("DB_PASS", "postgres")
	dbName := common.GetEnv("DB_NAME", "centralrepo")
	poolSize, _ := strconv.Atoi(common.GetEnv("DB_POOL_SIZE", "10"))
	maxIdleConnection, _ := strconv.Atoi(common.GetEnv("DB_MAX_IDLE_CONNECTION", "5"))
	idleConnectionTimeout, _ := strconv.Atoi(common.GetEnv("DB_IDLE_CONNECTION_TIMEOUT", "5"))
	recreateDB := common.GetEnv("RECREATE_DB", "false")
	logQuery := common.GetEnv("ENABLE_DB_QUERY_LOGGER", "false")

	dbConnString := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", dbUser, dbPass, dbHost, dbPort, dbName)

	config, err := pgxpool.ParseConfig(dbConnString)
	if err != nil {
		panic(err)
	}
	config.MaxConns = int32(poolSize)
	config.MinConns = int32(maxIdleConnection)
	config.MaxConnIdleTime = time.Duration(idleConnectionTimeout) * time.Minute

	pool, err := pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		panic(err)
	}
	sqlDB := stdlib.OpenDBFromPool(pool)
	Connection = bun.NewDB(sqlDB, pgdialect.New())
	logDbQuery, _ := strconv.ParseBool(logQuery)
	if logDbQuery {
		Connection.AddQueryHook(&dbLogger{})
	}
	reCreateDb, _ := strconv.ParseBool(recreateDB)
	if reCreateDb {
		err := dropAndCreateSchema(Connection, dbUser)
		if err != nil {
			return nil, err
		}
		err = createSchemaTable(Connection)
		if err != nil {
			return nil, err
		}
	}

	return Connection, nil
}

func dropAndCreateSchema(conn *bun.DB, dbUser string) error {
	ctx := context.Background()
	_, err := conn.NewSelect().ColumnExpr("schema_name").
		TableExpr("information_schema.schemata").
		Where("schema_name = 'public'").Exec(ctx)
	if err == nil {
		_, err = conn.Exec("DROP SCHEMA public CASCADE")
		if err != nil {
			return err
		}
	}
	_, err = conn.Exec("CREATE SCHEMA public AUTHORIZATION " + dbUser)
	return err
}

func createSchemaTable(conn *bun.DB) error {
	ctx := context.Background()
	for _, tableModel := range models() {
		_, err := conn.NewCreateTable().Model(tableModel).IfNotExists().Exec(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

func models() []interface{} {
	modelList := []interface{}{
		(*model.User)(nil),
		(*thirdparty.SupportedThirdPartyApplicationDetails)(nil),
		(*thirdparty.ThirdPartyPackage)(nil),
		(*windows.WindowsPatch)(nil),
		(*windows.PatchCategory)(nil),
		(*windows.PatchProduct)(nil),
		(*windows.WsusSyncHistory)(nil),
		(*windows.CabSyncHistory)(nil),
		(*windows.Language)(nil),
		(*windows.WsusUUID)(nil),
		(*ubuntu.UbuntuPatch)(nil),
		(*ubuntu.UbuntuNoticeData)(nil),
		(*ubuntu.UbuntuNoticeHistory)(nil),
		(*ubuntu.UbuntuReleasePackage)(nil),
		(*ubuntu.LinuxPackage)(nil),
		(*centos.CentosPatch)(nil),
		(*centos.CentosNoticeData)(nil),
		(*centos.CentosReleasePackage)(nil),
		(*macos.MacOsPatch)(nil),
	}
	return modelList
}
