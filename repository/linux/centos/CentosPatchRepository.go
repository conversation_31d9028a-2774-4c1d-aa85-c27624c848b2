package centos

import (
	"context"
	"github.com/uptrace/bun"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
)

type CentosPatchRepository struct {
	dbConnection *bun.DB
}

func NewCentosPatchRepository() *CentosPatchRepository {
	return &CentosPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo CentosPatchRepository) Create(centosPatch *centos.CentosPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(centosPatch).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository Create]", err.Error())
		return 0, err
	}
	return centosPatch.Id, nil
}

func (repo CentosPatchRepository) Update(centosPatch *centos.CentosPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(centosPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository Update]", err.Error())
		return 0, err
	}
	return centosPatch.Id, nil
}

func (repo CentosPatchRepository) GetById(id int64) (centos.CentosPatch, error) {
	var centosPatch centos.CentosPatch
	err := repo.dbConnection.NewSelect().Model(&centosPatch).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository GetById]", err.Error())
		return centosPatch, err
	}
	return centosPatch, nil
}

func (repo CentosPatchRepository) FindByUUID(uuid string) (centos.CentosPatch, error) {
	var centosPatch centos.CentosPatch
	err := repo.dbConnection.NewSelect().Model(&centosPatch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository FindByUUID]", err.Error())
		return centosPatch, err
	}
	return centosPatch, nil
}

func (repo CentosPatchRepository) FindByPackageNameAndVersion(packageName, version, osVersion string) (centos.CentosPatch, error) {
	var centosPatch centos.CentosPatch
	err := repo.dbConnection.NewSelect().Model(&centosPatch).
		Where("package_name = ?", packageName).
		Where("version = ?", version).
		Where("os_version = ?", osVersion).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository FindByPackageNameAndVersion]", err.Error())
		return centosPatch, err
	}
	return centosPatch, nil
}

func (repo CentosPatchRepository) DeleteById(id int64) (bool, error) {
	centosPatch := centos.CentosPatch{}
	centosPatch.Id = id
	_, err := repo.dbConnection.NewDelete().Model(centosPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo CentosPatchRepository) GetAllCentosPatches(query string) ([]centos.CentosPatch, error) {
	var centosPatches []centos.CentosPatch
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &centosPatches)
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository GetAllCentosPatches]", err.Error())
		return centosPatches, err
	}
	return centosPatches, nil
}

func (repo CentosPatchRepository) Count(query string) int64 {
	var count int64
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo CentosPatchRepository) GetAllUuidByCreatedTime(createdTime int64) ([]string, error) {
	var uuids []string
	err := repo.dbConnection.NewSelect().
		Model((*centos.CentosPatch)(nil)).
		Column("uuid").
		Where("created_time >= ?", createdTime).
		Scan(context.Background(), &uuids)
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository GetAllUuidByCreatedTime]", err.Error())
		return nil, err
	}
	return uuids, nil
}

func (repo CentosPatchRepository) GetPatchesByOsVersionAndRepo(osVersion, repoName string) ([]centos.CentosPatch, error) {
	var patches []centos.CentosPatch
	err := repo.dbConnection.NewSelect().Model(&patches).
		Where("os_version = ?", osVersion).
		Where("repo = ?", repoName).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosPatchRepository GetPatchesByOsVersionAndRepo]", err.Error())
		return nil, err
	}
	return patches, nil
}
