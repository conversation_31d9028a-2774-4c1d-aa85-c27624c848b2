package centos

import (
	"context"
	"github.com/uptrace/bun"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
)

type CentosNoticeDataRepository struct {
	dbConnection *bun.DB
}

func NewCentosNoticeDataRepository() *CentosNoticeDataRepository {
	return &CentosNoticeDataRepository{
		dbConnection: db.Connection,
	}
}

func (repo CentosNoticeDataRepository) Create(centosNoticeData *centos.CentosNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(centosNoticeData).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository Create]", err.Error())
		return 0, err
	}
	return centosNoticeData.Id, nil
}

func (repo CentosNoticeDataRepository) Update(centosNoticeData *centos.CentosNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(centosNoticeData).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository Update]", err.Error())
		return 0, err
	}
	return centosNoticeData.Id, nil
}

func (repo CentosNoticeDataRepository) GetById(id int64) (centos.CentosNoticeData, error) {
	var centosNoticeData centos.CentosNoticeData
	err := repo.dbConnection.NewSelect().Model(&centosNoticeData).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository GetById]", err.Error())
		return centosNoticeData, err
	}
	return centosNoticeData, nil
}

func (repo CentosNoticeDataRepository) FindByNoticeId(noticeId string) (centos.CentosNoticeData, error) {
	var centosNoticeData centos.CentosNoticeData
	err := repo.dbConnection.NewSelect().Model(&centosNoticeData).Where("notice_id = ?", noticeId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository FindByNoticeId]", err.Error())
		return centosNoticeData, err
	}
	return centosNoticeData, nil
}

func (repo CentosNoticeDataRepository) DeleteById(id int64) (bool, error) {
	centosNoticeData := centos.CentosNoticeData{}
	centosNoticeData.Id = id
	_, err := repo.dbConnection.NewDelete().Model(centosNoticeData).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo CentosNoticeDataRepository) GetAllCentosNoticeData(query string) ([]centos.CentosNoticeData, error) {
	var centosNoticeDataList []centos.CentosNoticeData
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &centosNoticeDataList)
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository GetAllCentosNoticeData]", err.Error())
		return centosNoticeDataList, err
	}
	return centosNoticeDataList, nil
}

func (repo CentosNoticeDataRepository) Count(query string) int64 {
	var count int64
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo CentosNoticeDataRepository) GetLatestNotices(limit int) ([]centos.CentosNoticeData, error) {
	var notices []centos.CentosNoticeData
	err := repo.dbConnection.NewSelect().Model(&notices).
		Order("release_date DESC").
		Limit(limit).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository GetLatestNotices]", err.Error())
		return nil, err
	}
	return notices, nil
}

func (repo CentosNoticeDataRepository) GetNoticesByType(noticeType string) ([]centos.CentosNoticeData, error) {
	var notices []centos.CentosNoticeData
	err := repo.dbConnection.NewSelect().Model(&notices).
		Where("type = ?", noticeType).
		Order("release_date DESC").
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosNoticeDataRepository GetNoticesByType]", err.Error())
		return nil, err
	}
	return notices, nil
}
