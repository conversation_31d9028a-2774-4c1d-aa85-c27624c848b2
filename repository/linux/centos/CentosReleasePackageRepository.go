package centos

import (
	"context"
	"github.com/uptrace/bun"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
)

type CentosReleasePackageRepository struct {
	dbConnection *bun.DB
}

func NewCentosReleasePackageRepository() *CentosReleasePackageRepository {
	return &CentosReleasePackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo CentosReleasePackageRepository) Create(centosReleasePackage *centos.CentosReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(centosReleasePackage).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository Create]", err.Error())
		return 0, err
	}
	return centosReleasePackage.Id, nil
}

func (repo CentosReleasePackageRepository) Update(centosReleasePackage *centos.CentosReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(centosReleasePackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository Update]", err.Error())
		return 0, err
	}
	return centosReleasePackage.Id, nil
}

func (repo CentosReleasePackageRepository) GetById(id int64) (centos.CentosReleasePackage, error) {
	var centosReleasePackage centos.CentosReleasePackage
	err := repo.dbConnection.NewSelect().Model(&centosReleasePackage).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository GetById]", err.Error())
		return centosReleasePackage, err
	}
	return centosReleasePackage, nil
}

func (repo CentosReleasePackageRepository) FindByNoticeId(noticeId string) ([]centos.CentosReleasePackage, error) {
	var centosReleasePackages []centos.CentosReleasePackage
	err := repo.dbConnection.NewSelect().Model(&centosReleasePackages).
		Where("notice_id = ?", noticeId).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository FindByNoticeId]", err.Error())
		return nil, err
	}
	return centosReleasePackages, nil
}

func (repo CentosReleasePackageRepository) FindByOSVersion(osVersion string) ([]centos.CentosReleasePackage, error) {
	var centosReleasePackages []centos.CentosReleasePackage
	err := repo.dbConnection.NewSelect().Model(&centosReleasePackages).
		Where("os_version = ?", osVersion).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository FindByOSVersion]", err.Error())
		return nil, err
	}
	return centosReleasePackages, nil
}

func (repo CentosReleasePackageRepository) FindByNameAndVersion(nameVersion string) ([]centos.CentosReleasePackage, error) {
	var centosReleasePackages []centos.CentosReleasePackage
	err := repo.dbConnection.NewSelect().Model(&centosReleasePackages).
		Where("name_and_version = ?", nameVersion).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository FindByNameAndVersion]", err.Error())
		return nil, err
	}
	return centosReleasePackages, nil
}

func (repo CentosReleasePackageRepository) DeleteById(id int64) (bool, error) {
	centosReleasePackage := centos.CentosReleasePackage{}
	centosReleasePackage.Id = id
	_, err := repo.dbConnection.NewDelete().Model(centosReleasePackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo CentosReleasePackageRepository) GetAllCentosReleasePackages(query string) ([]centos.CentosReleasePackage, error) {
	var centosReleasePackages []centos.CentosReleasePackage
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &centosReleasePackages)
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository GetAllCentosReleasePackages]", err.Error())
		return centosReleasePackages, err
	}
	return centosReleasePackages, nil
}

func (repo CentosReleasePackageRepository) Count(query string) int64 {
	var count int64
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo CentosReleasePackageRepository) GetPackagesByRepository(repository string) ([]centos.CentosReleasePackage, error) {
	var packages []centos.CentosReleasePackage
	err := repo.dbConnection.NewSelect().Model(&packages).
		Where("repository = ?", repository).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentosReleasePackageRepository GetPackagesByRepository]", err.Error())
		return nil, err
	}
	return packages, nil
}
