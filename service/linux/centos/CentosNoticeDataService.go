package centos

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/linux/centos"
	centosrepo "patch-central-repo/repository/linux/centos"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"strconv"
	"strings"
	"time"
)

type CentosNoticeDataService struct {
	Repository *centosrepo.CentosNoticeDataRepository
}

const (
	// CentOS Security Advisory URLs
	CentosSecurityUrl = "https://lists.centos.org/pipermail/centos-announce/"
	CentosErrata      = "https://cefs.steve-meier.de/errata.json"
)

func NewCentosNoticeDataService() *CentosNoticeDataService {
	return &CentosNoticeDataService{Repository: centosrepo.NewCentosNoticeDataRepository()}
}

// CentOS Errata structure from cefs.steve-meier.de
type CentosErratum struct {
	ID          string   `json:"id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Type        string   `json:"type"`
	Severity    string   `json:"severity"`
	References  []string `json:"references"`
	CVEs        []string `json:"cves"`
	Packages    []string `json:"packages"`
	IssueDate   string   `json:"issue_date"`
	UpdateDate  string   `json:"update_date"`
	OSVersions  []string `json:"os_versions"`
}

func (service CentosNoticeDataService) convertToModel(noticeInfo centos.CentosNoticeData) *centos.CentosNoticeData {
	return &centos.CentosNoticeData{
		BaseEntityModel: noticeInfo.BaseEntityModel,
		CVEsIDs:         noticeInfo.CVEsIDs,
		NoticeId:        noticeInfo.NoticeId,
		Description:     noticeInfo.Description,
		Instructions:    noticeInfo.Instructions,
		IsHidden:        noticeInfo.IsHidden,
		Published:       noticeInfo.Published,
		Summary:         noticeInfo.Summary,
		Title:           noticeInfo.Title,
		Type:            noticeInfo.Type,
		AffectedOS:      noticeInfo.AffectedOS,
		SupportURL:      noticeInfo.SupportURL,
		ReleaseDate:     noticeInfo.ReleaseDate,
		ReleasePackages: noticeInfo.ReleasePackages,
	}
}

func (service CentosNoticeDataService) SyncCentosNoticeData() {
	logger.ServiceLogger.Info("Process started to Sync CentOS Notice Data")
	
	// Fetch errata data from cefs.steve-meier.de
	err := service.fetchCentosErrata()
	if err != nil {
		logger.ServiceLogger.Error("Error fetching CentOS errata: ", err)
	}
	
	logger.ServiceLogger.Info("Process completed to Sync CentOS Notice Data")
}

func (service CentosNoticeDataService) fetchCentosErrata() error {
	resp, err := http.Get(CentosErrata)
	if err != nil {
		return fmt.Errorf("error fetching CentOS errata: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error %d when fetching CentOS errata", resp.StatusCode)
	}
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}
	
	var errataList []CentosErratum
	err = json.Unmarshal(body, &errataList)
	if err != nil {
		return fmt.Errorf("error parsing JSON: %w", err)
	}
	
	// Process each erratum
	for _, erratum := range errataList {
		service.processErratum(erratum)
	}
	
	return nil
}

func (service CentosNoticeDataService) processErratum(erratum CentosErratum) {
	// Check if notice already exists
	existingNotice, _ := service.Repository.FindByNoticeId(erratum.ID)
	if existingNotice.Id != 0 {
		return
	}
	
	// Parse issue date
	issueTime, err := time.Parse("2006-01-02", erratum.IssueDate)
	if err != nil {
		issueTime = time.Now()
	}
	
	// Create notice data
	noticeData := centos.CentosNoticeData{
		CVEsIDs:     erratum.CVEs,
		NoticeId:    erratum.ID,
		Description: erratum.Description,
		Instructions: fmt.Sprintf("Update the affected packages using: yum update %s", 
			strings.Join(erratum.Packages, " ")),
		IsHidden:    false,
		Published:   erratum.IssueDate,
		Summary:     erratum.Title,
		Title:       erratum.Title,
		Type:        erratum.Type,
		AffectedOS:  strings.Join(erratum.OSVersions, ", "),
		SupportURL:  "https://www.centos.org/",
		ReleaseDate: issueTime.UnixMilli(),
		CreatedTime: time.Now().UnixMilli(),
		UpdatedTime: time.Now().UnixMilli(),
	}
	
	// Create release packages map
	releasePackages := make(map[string][]centos.CentosReleasePackage)
	for _, osVersion := range erratum.OSVersions {
		var packages []centos.CentosReleasePackage
		for _, pkg := range erratum.Packages {
			releasePackage := centos.CentosReleasePackage{
				NoticeID:       erratum.ID,
				OSVersion:      osVersion,
				Description:    pkg,
				IsSource:       false,
				IsVisible:      true,
				Repository:     "updates",
				SourceLink:     "",
				Version:        "",
				VersionLink:    "",
				NameAndVersion: pkg,
				CreatedTime:    time.Now().UnixMilli(),
				UpdatedTime:    time.Now().UnixMilli(),
			}
			packages = append(packages, releasePackage)
		}
		releasePackages[osVersion] = packages
	}
	noticeData.ReleasePackages = releasePackages
	
	// Save to database
	_, err = service.Repository.Create(&noticeData)
	if err != nil {
		logger.ServiceLogger.Error("Error creating CentOS notice data: ", err)
	}
}

func (service CentosNoticeDataService) GetAllCentosNoticeData(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.CentosNoticeData.String(), true)
	var responsePage rest.ListResponseRest
	var noticeDataList []centos.CentosNoticeData
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.CentosNoticeData.String(), false)
		noticeDataList, err = service.Repository.GetAllCentosNoticeData(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = noticeDataList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service CentosNoticeDataService) Create(noticeInfo centos.CentosNoticeData) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create CentOS notice data")
	
	notice := service.convertToModel(noticeInfo)
	notice.CreatedTime = time.Now().UnixMilli()
	notice.UpdatedTime = time.Now().UnixMilli()
	id, err := service.Repository.Create(notice)
	if err != nil {
		logger.ServiceLogger.Error("Error while creating CentOS notice data: ", err.Error())
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}

func (service CentosNoticeDataService) GetNoticeDataById(id int64) (centos.CentosNoticeData, common.CustomError) {
	noticeData, err := service.Repository.GetById(id)
	if err != nil {
		logger.ServiceLogger.Error("Error while getting CentOS notice data by id: ", err.Error())
		return noticeData, common.CustomError{Message: err.Error()}
	}
	return noticeData, common.CustomError{}
}

func (service CentosNoticeDataService) GetLatestNotices(limit int) ([]centos.CentosNoticeData, error) {
	return service.Repository.GetLatestNotices(limit)
}

func (service CentosNoticeDataService) GetSecurityNotices() ([]centos.CentosNoticeData, error) {
	return service.Repository.GetNoticesByType("Security")
}
