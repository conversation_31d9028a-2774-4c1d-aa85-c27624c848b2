package centos

import (
	"patch-central-repo/common"
	"patch-central-repo/model/linux/centos"
	centosrepo "patch-central-repo/repository/linux/centos"
	"patch-central-repo/rest"
)

type CentosReleasePackageService struct {
	Repository *centosrepo.CentosReleasePackageRepository
}

func NewCentosReleasePackageService() *CentosReleasePackageService {
	return &CentosReleasePackageService{Repository: centosrepo.NewCentosReleasePackageRepository()}
}

func (service CentosReleasePackageService) convertToModel(packageInfo centos.CentosReleasePackage) *centos.CentosReleasePackage {
	return &centos.CentosReleasePackage{
		BaseEntityModel: packageInfo.BaseEntityModel,
		NoticeID:        packageInfo.NoticeID,
		OSVersion:       packageInfo.OSVersion,
		Description:     packageInfo.Description,
		IsSource:        packageInfo.IsSource,
		IsVisible:       packageInfo.IsVisible,
		Repository:      packageInfo.Repository,
		SourceLink:      packageInfo.SourceLink,
		Version:         packageInfo.Version,
		VersionLink:     packageInfo.VersionLink,
		NameAndVersion:  packageInfo.NameAndVersion,
	}
}

func (service CentosReleasePackageService) GetAllCentosReleasePackages(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.CentosReleasePackage.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []centos.CentosReleasePackage
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.CentosReleasePackage.String(), false)
		packagePageList, err = service.Repository.GetAllCentosReleasePackages(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service CentosReleasePackageService) Create(packageInfo centos.CentosReleasePackage) (int64, common.CustomError) {
	pkg := service.convertToModel(packageInfo)
	id, err := service.Repository.Create(pkg)
	if err != nil {
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}

func (service CentosReleasePackageService) Update(packageInfo centos.CentosReleasePackage) (int64, common.CustomError) {
	pkg := service.convertToModel(packageInfo)
	id, err := service.Repository.Update(pkg)
	if err != nil {
		return 0, common.CustomError{Message: err.Error()}
	}
	return id, common.CustomError{}
}

func (service CentosReleasePackageService) GetById(id int64) (centos.CentosReleasePackage, common.CustomError) {
	pkg, err := service.Repository.GetById(id)
	if err != nil {
		return pkg, common.CustomError{Message: err.Error()}
	}
	return pkg, common.CustomError{}
}

func (service CentosReleasePackageService) DeleteById(id int64) (bool, common.CustomError) {
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return false, common.CustomError{Message: err.Error()}
	}
	return success, common.CustomError{}
}

func (service CentosReleasePackageService) GetPackagesByNoticeId(noticeId string) ([]centos.CentosReleasePackage, error) {
	return service.Repository.FindByNoticeId(noticeId)
}

func (service CentosReleasePackageService) GetPackagesByOSVersion(osVersion string) ([]centos.CentosReleasePackage, error) {
	return service.Repository.FindByOSVersion(osVersion)
}

func (service CentosReleasePackageService) GetPackagesByRepository(repository string) ([]centos.CentosReleasePackage, error) {
	return service.Repository.GetPackagesByRepository(repository)
}
